#!/usr/bin/env python3
"""
Generate ArgoCD and Kubernetes manifests from CI/CD pipeline parameters
Python version for cross-platform compatibility
"""

import argparse
import os
import sys
import re
import base64
import json
from pathlib import Path

# Import dynamic environment configuration
try:
    from dynamic_environment_config import get_environment_config, get_cluster_config
except ImportError:
    print("Warning: dynamic_environment_config module not found. Using fallback configuration.")

    def get_environment_config(environment, app_type, project_id):
        return {}

    def get_cluster_config(environment):
        return {
            "server": "https://kubernetes.default.svc",
            "name": "in-cluster",
            "cluster_id": "default"
        }


def print_status(message, status_type="INFO"):
    """Print colored status messages"""
    colors = {
        "SUCCESS": "\033[32m",
        "ERROR": "\033[31m",
        "WARNING": "\033[33m",
        "INFO": "\033[36m"
    }
    reset = "\033[0m"
    color = colors.get(status_type, "")
    print(f"{color}[{status_type}] {message}{reset}")


def validate_project_id(project_id):
    """Validate project ID format"""
    if not re.match(r'^[a-z0-9-]+$', project_id):
        print_status(f"Invalid project ID format: {project_id}", "ERROR")
        print_status("Project ID must be lowercase alphanumeric with hyphens only", "ERROR")
        return False
    return True


def get_legacy_environment_config(environment, replicas):
    """Legacy environment configuration for backward compatibility"""
    if replicas == 0:
        replica_map = {"dev": 1, "staging": 2, "production": 3}
        replicas = replica_map.get(environment, 1)

    config_map = {
        "dev": {
            "memory_request": "256Mi",
            "memory_limit": "512Mi",
            "cpu_request": "100m",
            "cpu_limit": "500m"
        },
        "staging": {
            "memory_request": "512Mi",
            "memory_limit": "1Gi",
            "cpu_request": "200m",
            "cpu_limit": "1000m"
        },
        "production": {
            "memory_request": "1Gi",
            "memory_limit": "2Gi",
            "cpu_request": "500m",
            "cpu_limit": "2000m"
        }
    }

    config = config_map.get(environment, config_map["dev"])
    config["replicas"] = replicas

    return config


def get_legacy_cluster_config(environment):
    """Legacy cluster configuration for backward compatibility"""
    cluster_mappings = {
        "dev": {
            "server": "https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com",
            "name": "doks-target-cluster",
            "cluster_id": "6be4e15d-52f9-431d-84ec-ec8cad0dff2d"
        },
        "staging": {
            "server": "https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com",
            "name": "doks-target-cluster",
            "cluster_id": "6be4e15d-52f9-431d-84ec-ec8cad0dff2d"
        },
        "production": {
            "server": "https://e9d23ae8-213c-4746-b379-330f85c0a0cf.k8s.ondigitalocean.com",
            "name": "production-cluster",
            "cluster_id": "e9d23ae8-213c-4746-b379-330f85c0a0cf"
        }
    }

    return cluster_mappings.get(environment, cluster_mappings["production"])










def encode_base64(value):
    """Encode a string value to base64"""
    if isinstance(value, str):
        return base64.b64encode(value.encode('utf-8')).decode('utf-8')
    return base64.b64encode(str(value).encode('utf-8')).decode('utf-8')


def decode_secrets_payload(secrets_encoded):
    """
    Decode base64 encoded secrets JSON payload.

    The payload now includes both secrets and application configuration:
    {
        "JWT_SECRET": "value",
        "DB_USER": "value",
        "DB_PASSWORD": "value",
        "SMTP_USER": "value",
        "SMTP_PASS": "value",
        "GOOGLE_CLIENT_ID": "value",
        "GOOGLE_CLIENT_SECRET": "value",
        "container_port": value,
        "health_check_path": "/value",
        "enable_database": true/false,
        "backend_type": "optional-backend-type",
        "backend_project_id": "optional-backend-project-id",
        "backend_port": "optional-backend-port"
    }
    """
    import json
    try:
        if not secrets_encoded:
            return {}

        # Decode base64 to get JSON string
        decoded_json = base64.b64decode(secrets_encoded).decode('utf-8')

        # Parse JSON to get secrets dictionary
        payload = json.loads(decoded_json)

        print_status(f"Decoded {len(payload)} items from secrets payload", "INFO")
        return payload

    except Exception as e:
        print_status(f"Failed to decode secrets payload: {str(e)}", "WARNING")
        return {}


def extract_application_config_from_secrets(secrets_payload, args):
    """
    Extract application-specific configuration from secrets payload.
    This allows for dynamic configuration without hardcoded command-line arguments.

    Args:
        secrets_payload: Decoded secrets payload dictionary
        args: Command-line arguments (for fallback values)

    Returns:
        Updated args object with configuration from secrets payload
    """
    # Extract container port from secrets payload
    if 'container_port' in secrets_payload:
        args.container_port = int(secrets_payload['container_port'])
        print_status(f"Using container_port from secrets: {args.container_port}", "INFO")

    # Extract health check path from secrets payload
    if 'health_check_path' in secrets_payload:
        args.health_check_path = secrets_payload['health_check_path']
        print_status(f"Using health_check_path from secrets: {args.health_check_path}", "INFO")

    # Extract database configuration from secrets payload
    if 'enable_database' in secrets_payload:
        args.enable_database = bool(secrets_payload['enable_database'])
        print_status(f"Using enable_database from secrets: {args.enable_database}", "INFO")

    # Extract backend configuration for frontend applications
    if 'backend_type' in secrets_payload:
        args.backend_type = secrets_payload['backend_type']
        print_status(f"Using backend_type from secrets: {args.backend_type}", "INFO")

    if 'backend_project_id' in secrets_payload:
        args.backend_project_id = secrets_payload['backend_project_id']
        print_status(f"Using backend_project_id from secrets: {args.backend_project_id}", "INFO")

    if 'backend_port' in secrets_payload:
        args.backend_port = int(secrets_payload['backend_port'])
        print_status(f"Using backend_port from secrets: {args.backend_port}", "INFO")

    return args








def generate_backend_service_url(backend_project_id, backend_port, environment, namespace_suffix=""):
    """
    Generate dynamic backend service URL for any application type.

    Args:
        backend_project_id: The project ID of the backend application
        backend_port: The port the backend service runs on
        environment: Target environment (dev, staging, production)
        namespace_suffix: Optional namespace suffix for the backend

    Returns:
        Complete service URL for inter-application communication
    """
    if environment == "dev":
        # For development, use LoadBalancer external IPs or NodePort access
        # This allows for flexible development setups
        return f"http://{backend_project_id}-service.{backend_project_id}-{environment}{namespace_suffix}.svc.cluster.local:{backend_port}"
    else:
        # For staging/production, use internal service discovery
        backend_namespace = f"{backend_project_id}-{environment}{namespace_suffix}"
        service_name = f"{backend_project_id}-service"
        return f"http://{service_name}.{backend_namespace}.svc.cluster.local:{backend_port}"


def get_dynamic_backend_configuration(backend_type, backend_project_id, backend_port, environment):
    """
    Get dynamic backend configuration for any application type.
    This replaces the hardcoded backend type mapping with a flexible approach.

    Args:
        backend_type: Type identifier for the backend (can be any string)
        backend_project_id: Project ID of the backend application
        backend_port: Port the backend service runs on
        environment: Target environment

    Returns:
        Dictionary containing backend configuration
    """
    backend_url = generate_backend_service_url(backend_project_id, backend_port, environment)

    return {
        'type': backend_type,
        'project_id': backend_project_id,
        'url': backend_url,
        'port': backend_port,
        'namespace': f"{backend_project_id}-{environment}",
        'service_name': f"{backend_project_id}-service"
    }


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Generate ArgoCD and Kubernetes manifests from CI/CD pipeline parameters"
    )

    parser.add_argument('--app-name', required=True, help='Application name')
    parser.add_argument('--project-id', required=True, help='Project identifier (lowercase alphanumeric with hyphens)')
    parser.add_argument('--environment', required=True, choices=['dev', 'staging', 'production', 'feature'], help='Target environment')
    parser.add_argument('--docker-image', required=True, help='Docker image repository')
    parser.add_argument('--docker-tag', required=True, help='Docker image tag')
    parser.add_argument('--application-type', '--app-type', default='web-app',
                       help='Application type (can be any string - no restrictions)')
    parser.add_argument('--container-port', type=int, default=8080, help='Container port (can be overridden by secrets payload)')
    parser.add_argument('--replicas', type=int, default=0, help='Number of replicas (0 = auto)')
    parser.add_argument('--enable-database', action='store_true', default=True, help='Enable database setup (can be overridden by secrets payload)')
    parser.add_argument('--disable-database', action='store_true', help='Disable database setup')
    parser.add_argument('--health-check-path', help='Health check endpoint path (can be overridden by secrets payload)')
    parser.add_argument('--backend-type', help='Backend type identifier for frontend applications')
    parser.add_argument('--backend-project-id', help='Project ID of the backend application to connect to')
    parser.add_argument('--backend-port', type=int, help='Port of the backend application to connect to')
    parser.add_argument('--db-host', help='Database host (overrides default managed PostgreSQL host)')
    parser.add_argument('--db-port', help='Database port (overrides default PostgreSQL port 5432)')
    parser.add_argument('--database-name', help='Database name (overrides default project-based name)')
    parser.add_argument('--source-repo', default='', help='Source repository')
    parser.add_argument('--source-branch', default='', help='Source branch')
    parser.add_argument('--commit-sha', default='', help='Commit SHA')
    parser.add_argument('--secrets-encoded', help='Base64 encoded secrets JSON payload')
    parser.add_argument('--output-dir', default='.', help='Output directory')

    args = parser.parse_args()

    # Handle database enable/disable logic
    if args.disable_database:
        args.enable_database = False

    # Disable database for backend applications (use managed DigitalOcean PostgreSQL)
    if args.application_type in ['springboot-backend', 'django-backend', 'nest-backend']:
        args.enable_database = False
        print_status(f"Automatically disabled database for {args.application_type} - using managed DigitalOcean PostgreSQL", "INFO")

    # Set default values that can be overridden by secrets payload
    # These are fallback values only - the secrets payload takes precedence

    # Set default container port (will be overridden by secrets if provided)
    if not hasattr(args, 'container_port') or args.container_port == 8080:
        # Use 8080 as universal default - secrets payload will override with correct port
        print_status(f"Using default container port: {args.container_port} (can be overridden by secrets)", "INFO")

    # Set default health check path (will be overridden by secrets if provided)
    if not args.health_check_path:
        # Use /health as universal default - secrets payload will override with correct path
        args.health_check_path = '/health'
        print_status(f"Using default health check path: {args.health_check_path} (can be overridden by secrets)", "INFO")

    # Initialize backend configuration attributes
    if not hasattr(args, 'backend_project_id'):
        args.backend_project_id = None
    if not hasattr(args, 'backend_port'):
        args.backend_port = None

    return args


def generate_dynamic_api_url(args):
    """
    Generate dynamic API URL based on application configuration.

    For frontend applications, this generates the backend API URL.
    For backend applications, this generates the self-service URL.

    Args:
        args: Parsed command-line arguments with backend configuration

    Returns:
        API URL string for the application
    """
    # Check if this is a frontend application that needs to connect to a backend
    frontend_types = ['react-frontend', 'vue-frontend', 'angular-frontend', 'web-app', 'frontend']

    if any(frontend_type in args.application_type.lower() for frontend_type in frontend_types):
        # This is a frontend application - generate backend API URL
        if args.backend_project_id and args.backend_port:
            # Use dynamic backend configuration from secrets payload
            return generate_backend_service_url(
                args.backend_project_id,
                args.backend_port,
                args.environment
            )
        elif args.backend_type:
            # Fallback to legacy backend type mapping for backward compatibility
            legacy_backend_config = get_legacy_backend_config(args.backend_type, args.environment)
            return legacy_backend_config.get('url', f"http://{args.project_id}-service:{args.container_port}")
        else:
            # No backend specified - use self-service URL
            return f"http://{args.project_id}-service:{args.container_port}"
    else:
        # This is a backend application - use self-service URL
        return f"http://{args.project_id}-service:{args.container_port}"


def get_legacy_backend_config(backend_type, environment):
    """
    Get legacy backend configuration for backward compatibility.
    This maintains compatibility with existing deployments.

    Args:
        backend_type: Legacy backend type (spring, django, nest)
        environment: Target environment

    Returns:
        Dictionary with legacy backend configuration
    """
    if environment == "dev":
        # Legacy external IPs for development
        legacy_configs = {
            'spring': {'url': 'http://*************:8080', 'port': 8080},
            'django': {'url': 'http://***************:8000', 'port': 8000},
            'nest': {'url': 'http://*************:3000', 'port': 3000}
        }
        return legacy_configs.get(backend_type, {'url': 'http://*************:8080', 'port': 8080})
    else:
        # Legacy internal service names for staging/production
        legacy_configs = {
            'spring': {'url': 'http://ai-spring-backend-service:8080', 'port': 8080},
            'django': {'url': 'http://ai-django-backend-service:8000', 'port': 8000},
            'nest': {'url': 'http://ai-nest-backend-service:3000', 'port': 3000}
        }
        return legacy_configs.get(backend_type, {'url': 'http://default-backend-service:8080', 'port': 8080})


def generate_kustomize_overlay(args, template_vars, project_dir):
    """Generate Kustomize overlay for the specified environment"""
    try:
        print_status(f"Generating Kustomize overlay for environment: {args.environment}", "INFO")

        # Create overlay directory
        overlay_dir = os.path.join(project_dir, 'k8s', 'overlays', args.environment)
        os.makedirs(overlay_dir, exist_ok=True)

        # Generate environment-specific kustomization.yaml
        kustomization_content = generate_kustomization_yaml(args, template_vars)
        kustomization_path = os.path.join(overlay_dir, 'kustomization.yaml')

        with open(kustomization_path, 'w') as f:
            f.write(kustomization_content)

        print_status(f"Generated kustomization.yaml: {kustomization_path}", "SUCCESS")

        # Generate environment-specific patches
        patches = generate_environment_patches(args, template_vars)

        for patch_name, patch_content in patches.items():
            patch_path = os.path.join(overlay_dir, patch_name)
            with open(patch_path, 'w') as f:
                f.write(patch_content)
            print_status(f"Generated patch: {patch_path}", "SUCCESS")

        # Generate ArgoCD application for Kustomize
        argocd_dir = os.path.join(project_dir, 'argocd')
        os.makedirs(argocd_dir, exist_ok=True)

        argocd_app_content = generate_argocd_kustomize_application(args, template_vars)
        argocd_app_path = os.path.join(argocd_dir, 'application.yaml')

        with open(argocd_app_path, 'w') as f:
            f.write(argocd_app_content)

        print_status(f"Generated ArgoCD application: {argocd_app_path}", "SUCCESS")

        return True

    except Exception as e:
        print_status(f"Error generating Kustomize overlay: {str(e)}", "ERROR")
        import traceback
        print_status(f"Traceback: {traceback.format_exc()}", "ERROR")
        return False


def generate_dynamic_service_discovery_config(args, template_vars):
    """
    Generate dynamic service discovery configuration for inter-application communication.

    Args:
        args: Parsed command-line arguments
        template_vars: Template variables dictionary

    Returns:
        Dictionary containing service discovery configuration
    """
    service_discovery_config = {
        # Current application service information
        'current_service': {
            'name': f"{args.project_id}-service",
            'namespace': f"{args.project_id}-{args.environment}",
            'port': args.container_port,
            'type': args.application_type,
            'health_path': args.health_check_path
        }
    }

    # Add backend service configuration if this is a frontend application
    if getattr(args, 'backend_project_id', None):
        service_discovery_config['backend_service'] = {
            'name': f"{args.backend_project_id}-service",
            'namespace': f"{args.backend_project_id}-{args.environment}",
            'port': getattr(args, 'backend_port', 8080),
            'type': getattr(args, 'backend_type', 'backend'),
            'url': generate_backend_service_url(
                args.backend_project_id,
                getattr(args, 'backend_port', 8080),
                args.environment
            )
        }

    return service_discovery_config


def generate_kustomization_yaml(args, template_vars):
    """Generate kustomization.yaml content for the environment"""

    # Remove Prometheus-related variables as requested
    filtered_vars = {k: v for k, v in template_vars.items()
                    if not k.startswith('PROMETHEUS_') and k != 'GRAFANA_ENABLED'}

    # Generate service discovery configuration
    service_discovery = generate_dynamic_service_discovery_config(args, template_vars)

    kustomization = f"""apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: {args.environment}-overlay
  annotations:
    config.kubernetes.io/local-config: "true"

# Reference to base configuration
resources:
- ../../../templates/k8s/base

# Environment-specific namespace
namespace: {args.project_id}-{args.environment}

# Environment-specific name prefix
namePrefix: ""

# Common labels for {args.environment} environment
commonLabels:
  environment: {args.environment}
  deployment.tier: {args.environment}

# Common annotations for {args.environment} environment
commonAnnotations:
  argocd.argoproj.io/sync-options: "CreateNamespace=true"
  deployment.environment: "{args.environment}"

# {args.environment.title()}-specific image configuration
images:
- name: {args.docker_image}
  newTag: {args.docker_tag}

# ConfigMap generator for environment-specific variables
configMapGenerator:
- name: kustomize-vars
  literals:"""

    # Add all template variables as literals
    for key, value in sorted(filtered_vars.items()):
        if isinstance(value, str) and value:
            kustomization += f"\n  - {key}={value}"

    # Add service discovery configuration
    current_service = service_discovery['current_service']
    kustomization += f"\n  - SERVICE_NAME={current_service['name']}"
    kustomization += f"\n  - SERVICE_NAMESPACE={current_service['namespace']}"
    kustomization += f"\n  - SERVICE_PORT={current_service['port']}"
    kustomization += f"\n  - SERVICE_TYPE={current_service['type']}"
    kustomization += f"\n  - SERVICE_HEALTH_PATH={current_service['health_path']}"

    # Add backend service configuration if available
    if 'backend_service' in service_discovery:
        backend_service = service_discovery['backend_service']
        kustomization += f"\n  - BACKEND_SERVICE_NAME={backend_service['name']}"
        kustomization += f"\n  - BACKEND_SERVICE_NAMESPACE={backend_service['namespace']}"
        kustomization += f"\n  - BACKEND_SERVICE_PORT={backend_service['port']}"
        kustomization += f"\n  - BACKEND_SERVICE_TYPE={backend_service['type']}"
        kustomization += f"\n  - BACKEND_SERVICE_URL={backend_service['url']}"

    kustomization += f"""

# Environment-specific patches
patches:
- path: deployment-patch.yaml
  target:
    kind: Deployment
    name: {args.project_id}
- path: resource-patch.yaml
  target:
    kind: ResourceQuota
    name: {args.project_id}-resource-quota
- path: security-patch.yaml
  target:
    kind: Deployment
    name: {args.project_id}"""

    # Add HPA and PDB patches for staging and production
    if args.environment in ['staging', 'production']:
        kustomization += f"""
- path: hpa-patch.yaml
  target:
    kind: HorizontalPodAutoscaler
    name: {args.project_id}-hpa
- path: pdb-patch.yaml
  target:
    kind: PodDisruptionBudget
    name: {args.project_id}-pdb"""

    # Add patches to disable resources in development
    if args.environment == 'dev':
        kustomization += f"""

# Remove HPA in development (disabled)
patchesJson6902:
- target:
    version: v2
    kind: HorizontalPodAutoscaler
    name: {args.project_id}-hpa
  path: disable-hpa-patch.yaml

# Remove Network Policy in development (disabled)
- target:
    version: v1
    kind: NetworkPolicy
    name: {args.project_id}-network-policy
  path: disable-network-policy-patch.yaml"""

    return kustomization


def generate_environment_patches(args, template_vars):
    """Generate environment-specific patches"""
    patches = {}

    # Get environment-specific configuration
    try:
        env_config = get_environment_config(args.environment, args.application_type, args.project_id)
    except:
        env_config = {}

    # Generate deployment patch
    deployment_patch = f"""apiVersion: apps/v1
kind: Deployment
metadata:
  name: {args.project_id}
spec:
  replicas: {env_config.get('REPLICAS_DEFAULT', '2')}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: {env_config.get('ROLLING_UPDATE_MAX_UNAVAILABLE', '50%')}
      maxSurge: {env_config.get('ROLLING_UPDATE_MAX_SURGE', '50%')}
  progressDeadlineSeconds: {env_config.get('ROLLING_UPDATE_PROGRESS_DEADLINE', '120')}
  revisionHistoryLimit: {env_config.get('ROLLING_UPDATE_REVISION_HISTORY', '3')}
  template:
    spec:
      containers:
      - name: {args.project_id}
        resources:
          requests:
            memory: "{env_config.get('MEMORY_REQUEST', '256Mi')}"
            cpu: "{env_config.get('CPU_REQUEST', '200m')}"
          limits:
            memory: "{env_config.get('MEMORY_LIMIT', '512Mi')}"
            cpu: "{env_config.get('CPU_LIMIT', '500m')}"
        readinessProbe:
          httpGet:
            path: {template_vars.get('HEALTH_CHECK_PATH', '/health')}
            port: {args.container_port}
            scheme: HTTP
          initialDelaySeconds: {env_config.get('READINESS_PROBE_INITIAL_DELAY', '5')}
          periodSeconds: {env_config.get('READINESS_PROBE_PERIOD', '3')}
          timeoutSeconds: {env_config.get('READINESS_PROBE_TIMEOUT', '2')}
          failureThreshold: {env_config.get('READINESS_PROBE_FAILURE_THRESHOLD', '3')}
        livenessProbe:
          httpGet:
            path: {template_vars.get('HEALTH_CHECK_PATH', '/health')}
            port: {args.container_port}
            scheme: HTTP
          initialDelaySeconds: {env_config.get('LIVENESS_PROBE_INITIAL_DELAY', '15')}
          periodSeconds: {env_config.get('LIVENESS_PROBE_PERIOD', '10')}
          timeoutSeconds: {env_config.get('LIVENESS_PROBE_TIMEOUT', '5')}
          failureThreshold: {env_config.get('LIVENESS_PROBE_FAILURE_THRESHOLD', '3')}"""

    # Add startup probe for staging and production
    if args.environment in ['staging', 'production']:
        deployment_patch += f"""
        startupProbe:
          httpGet:
            path: {template_vars.get('HEALTH_CHECK_PATH', '/health')}
            port: {args.container_port}
            scheme: HTTP
          initialDelaySeconds: {env_config.get('STARTUP_PROBE_INITIAL_DELAY', '10')}
          periodSeconds: {env_config.get('STARTUP_PROBE_PERIOD', '5')}
          timeoutSeconds: {env_config.get('STARTUP_PROBE_TIMEOUT', '3')}
          failureThreshold: {env_config.get('STARTUP_PROBE_FAILURE_THRESHOLD', '5')}"""

    patches['deployment-patch.yaml'] = deployment_patch

    # Generate resource quota patch
    resource_quotas = {
        'dev': {'cpu_requests': '2000m', 'cpu_limits': '4000m', 'memory_requests': '4Gi', 'memory_limits': '8Gi', 'pods': '10'},
        'staging': {'cpu_requests': '3000m', 'cpu_limits': '6000m', 'memory_requests': '6Gi', 'memory_limits': '12Gi', 'pods': '15'},
        'production': {'cpu_requests': '3500m', 'cpu_limits': '8000m', 'memory_requests': '7Gi', 'memory_limits': '16Gi', 'pods': '20'}
    }

    quota = resource_quotas.get(args.environment, resource_quotas['dev'])

    resource_patch = f"""apiVersion: v1
kind: ResourceQuota
metadata:
  name: {args.project_id}-resource-quota
spec:
  hard:
    requests.cpu: "{quota['cpu_requests']}"
    limits.cpu: "{quota['cpu_limits']}"
    requests.memory: "{quota['memory_requests']}"
    limits.memory: "{quota['memory_limits']}"
    requests.storage: "50Gi"
    pods: "{quota['pods']}"
    services: "5"
    secrets: "10"
    configmaps: "10"
    persistentvolumeclaims: "5"
    services.loadbalancers: "2"
    services.nodeports: "3" """

    patches['resource-patch.yaml'] = resource_patch

    # Generate security patch
    if args.environment == 'dev':
        security_patch = f"""apiVersion: apps/v1
kind: Deployment
metadata:
  name: {args.project_id}
spec:
  template:
    spec:
      securityContext:
        runAsNonRoot: false
      containers:
      - name: {args.project_id}
        securityContext:
          runAsNonRoot: false
          readOnlyRootFilesystem: false
          allowPrivilegeEscalation: true"""
    else:
        fsgroup_line = "        fsGroup: 1000" if args.environment == "production" else ""
        security_patch = f"""apiVersion: apps/v1
kind: Deployment
metadata:
  name: {args.project_id}
spec:
  template:
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        {fsgroup_line}
      containers:
      - name: {args.project_id}
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          runAsGroup: 1000
          readOnlyRootFilesystem: true
          allowPrivilegeEscalation: false
          {"capabilities:\n            drop: [\"ALL\"]" if args.environment == "production" else ""}"""

    patches['security-patch.yaml'] = security_patch

    # Generate HPA and PDB patches for staging and production
    if args.environment in ['staging', 'production']:
        hpa_config = {
            'staging': {'min': 3, 'max': 6, 'scale_down_percent': 10, 'scale_up_percent': 25},
            'production': {'min': 3, 'max': 10, 'scale_down_percent': 5, 'scale_up_percent': 10}
        }

        hpa = hpa_config[args.environment]
        hpa_patch = f"""apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {args.project_id}-hpa
spec:
  minReplicas: {hpa['min']}
  maxReplicas: {hpa['max']}
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: {hpa['scale_down_percent']}
        periodSeconds: {"120" if args.environment == "production" else "60"}
      selectPolicy: Min
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: {hpa['scale_up_percent']}
        periodSeconds: {"60" if args.environment == "production" else "30"}
      selectPolicy: Max"""

        patches['hpa-patch.yaml'] = hpa_patch

        pdb_patch = f"""apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {args.project_id}-pdb
spec:
  {"maxUnavailable: 1" if args.environment == "production" else "maxUnavailable: 25%"}"""

        patches['pdb-patch.yaml'] = pdb_patch

    # Generate disable patches for development
    if args.environment == 'dev':
        patches['disable-hpa-patch.yaml'] = """- op: replace
  path: /metadata/annotations/argocd.argoproj.io~1sync-options
  value: "Prune=false" """

        patches['disable-network-policy-patch.yaml'] = """- op: replace
  path: /metadata/annotations/argocd.argoproj.io~1sync-options
  value: "Prune=false" """

    return patches


def generate_argocd_kustomize_application(args, template_vars):
    """Generate ArgoCD application for Kustomize deployment"""

    # Get cluster configuration
    try:
        cluster_config = get_cluster_config(args.environment)
    except:
        cluster_config = get_legacy_cluster_config(args.environment)

    return f"""apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {args.project_id}-{args.environment}
  namespace: argocd
  labels:
    app: {args.project_id}
    environment: {args.environment}
    app.kubernetes.io/name: {args.project_id}
    app.kubernetes.io/component: {args.application_type}
    app.kubernetes.io/managed-by: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "0"
spec:
  project: default
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps.git
    targetRevision: HEAD
    path: {args.project_id}/k8s/overlays/{args.environment}
  destination:
    server: {cluster_config["server"]}
    namespace: {args.project_id}-{args.environment}
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
    - CreateNamespace=true
    - PrunePropagationPolicy=foreground
    - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 10"""


def main():
    """Main function"""
    args = parse_arguments()

    try:
        print_status("Starting manifest generation from CI/CD parameters", "INFO")
        print_status(f"Application: {args.app_name}", "INFO")
        print_status(f"Project ID: {args.project_id}", "INFO")
        print_status(f"Environment: {args.environment}", "INFO")
        print_status(f"Docker Image: {args.docker_image}:{args.docker_tag}", "INFO")

        # Validate inputs
        if not validate_project_id(args.project_id):
            sys.exit(1)

        # Get dynamic environment configuration
        try:
            dynamic_config = get_environment_config(args.environment, args.application_type, args.project_id)
            print_status("Using dynamic environment configuration", "INFO")
        except Exception as e:
            print_status(f"Failed to load dynamic config, using legacy: {e}", "WARNING")
            dynamic_config = {}

        # Get legacy environment configuration for backward compatibility
        legacy_env_config = get_legacy_environment_config(args.environment, args.replicas)

        # Get cluster configuration
        try:
            cluster_config = get_cluster_config(args.environment)
        except Exception as e:
            print_status(f"Failed to load dynamic cluster config, using legacy: {e}", "WARNING")
            cluster_config = get_legacy_cluster_config(args.environment)

        # Set up paths
        project_dir = os.path.join(args.output_dir, args.project_id)
        argocd_dir = os.path.join(project_dir, "argocd")
        k8s_dir = os.path.join(project_dir, "k8s")

        # Create directories
        os.makedirs(argocd_dir, exist_ok=True)
        os.makedirs(k8s_dir, exist_ok=True)

        # Decode secrets if provided
        secrets = decode_secrets_payload(args.secrets_encoded) if args.secrets_encoded else {}

        # Extract application configuration from secrets payload
        # This allows dynamic configuration without hardcoded command-line arguments
        args = extract_application_config_from_secrets(secrets, args)

        # Build comprehensive template variables dictionary
        # Start with dynamic configuration
        template_vars = dynamic_config.copy()

        # Override with specific CI/CD parameters
        template_vars.update({
            'APP_NAME': args.app_name,
            'PROJECT_ID': args.project_id,
            'NAMESPACE': f"{args.project_id}-{args.environment}",
            'CONTAINER_IMAGE': f"{args.docker_image}:{args.docker_tag}",
            'DOCKER_IMAGE': args.docker_image,
            'DOCKER_TAG': args.docker_tag,
            'ENVIRONMENT': args.environment,
            'APP_TYPE': args.application_type,
            'APPLICATION_TYPE': args.application_type,
            'CONTAINER_PORT': str(args.container_port),
            'CLUSTER_SERVER': cluster_config["server"],
            'CLUSTER_NAME': cluster_config["name"],
            'CLUSTER_ID': cluster_config["cluster_id"],
            'ENABLE_DATABASE': str(args.enable_database).lower(),
            'SOURCE_REPO': args.source_repo,
            'SOURCE_BRANCH': args.source_branch,
            'COMMIT_SHA': args.commit_sha,

            # Dynamic Backend Configuration
            'BACKEND_TYPE': getattr(args, 'backend_type', '') or '',
            'BACKEND_PROJECT_ID': getattr(args, 'backend_project_id', '') or '',
            'BACKEND_PORT': str(getattr(args, 'backend_port', '')) if getattr(args, 'backend_port', None) else '',
            'BACKEND_NAMESPACE': f"{getattr(args, 'backend_project_id', '')}-{args.environment}" if getattr(args, 'backend_project_id', None) else '',

            # Service Discovery Configuration
            'SERVICE_NAME': f"{args.project_id}-service",
            'SERVICE_NAMESPACE': f"{args.project_id}-{args.environment}",
            'SERVICE_PORT': str(args.container_port),
        })

        # Add legacy configuration for backward compatibility
        if not dynamic_config:
            template_vars.update({
                'REPLICAS': str(legacy_env_config['replicas']),
                'MEMORY_REQUEST': legacy_env_config['memory_request'],
                'MEMORY_LIMIT': legacy_env_config['memory_limit'],
                'CPU_REQUEST': legacy_env_config['cpu_request'],
                'CPU_LIMIT': legacy_env_config['cpu_limit'],
            })

        # Add health check path (override dynamic config if provided)
        if args.health_check_path:
            template_vars['HEALTH_CHECK_PATH'] = args.health_check_path

        # Add database configuration with dynamic values from payload
        db_host = args.db_host if args.db_host else "*************"  # Default to managed DigitalOcean PostgreSQL
        db_port = args.db_port if args.db_port else "5432"  # Default PostgreSQL port
        db_name = args.database_name if args.database_name else args.project_id.replace('-', '_')  # Default to project-based name

        template_vars.update({
            'DB_USER': secrets.get('DB_USER', 'postgres'),
            'DB_NAME': db_name,
            'DB_HOST': db_host,
            'DB_PORT': db_port,
            'DB_PASSWORD': secrets.get('DB_PASSWORD', 'password'),
        })

        # Add authentication secrets
        template_vars.update({
            'JWT_SECRET': secrets.get('JWT_SECRET', 'supersecretkey'),
            'SMTP_USER': secrets.get('SMTP_USER', '<EMAIL>'),
            'SMTP_PASS': secrets.get('SMTP_PASS', 'fqactehafmzlltzz'),
            'GOOGLE_CLIENT_ID': secrets.get('GOOGLE_CLIENT_ID', '1073981864538-3uiik72ohsfr2ouioror3fm1jqc493os.apps.googleusercontent.com'),
            'GOOGLE_CLIENT_SECRET': secrets.get('GOOGLE_CLIENT_SECRET', '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'),
        })

        # Add base64 encoded secrets for templates
        template_vars.update({
            'INGRESS_HOST': '',
            'INGRESS_PATH': '/',
            # Missing variables identified from template analysis
            'API_URL': generate_dynamic_api_url(args),
            'APP_URL': f"http://{args.project_id}.{args.environment}.local",
            'APP_VERSION': '1.0.0',
            'CORS_ORIGINS': 'http://localhost:3000,http://localhost:3001',
            'ENABLE_INGRESS': 'false',
            'ENABLE_PVC': 'false',
            'GOOGLE_REDIRECT_URI': f"http://{args.project_id}.{args.environment}.local/auth/google/callback",
            'JAVA_XMS': '256m',
            'JAVA_XMX': '512m',
            'JWT_EXPIRATION': '24h',
            'OAUTH_REDIRECT_URIS': f"http://{args.project_id}.{args.environment}.local/auth/google/callback",
            'OAUTH_SCOPES': 'openid,profile,email',
            'PUBLIC_URL': f"http://{args.project_id}.{args.environment}.local",
            'PVC_SIZE': '1Gi',
            'SMTP_FROM': '<EMAIL>',
            'SMTP_HOST': 'smtp.gmail.com',
            'SMTP_PORT': '587',
            'STORAGE_SIZE': '5Gi'
        })

        # Add base64 encoded values for secrets
        template_vars.update({
            'DB_USER_B64': encode_base64(template_vars['DB_USER']),
            'DB_PASSWORD_B64': encode_base64(template_vars['DB_PASSWORD']),
            'JWT_SECRET_B64': encode_base64(template_vars['JWT_SECRET']),
            'SMTP_USER_B64': encode_base64(template_vars['SMTP_USER']),
            'SMTP_PASS_B64': encode_base64(template_vars['SMTP_PASS']),
            'GOOGLE_CLIENT_ID_B64': encode_base64(template_vars['GOOGLE_CLIENT_ID']),
            'GOOGLE_CLIENT_SECRET_B64': encode_base64(template_vars['GOOGLE_CLIENT_SECRET'])
        })

        print_status("Template variables prepared", "SUCCESS")

        # Generate Kustomize overlay (single deployment method)
        print_status("Generating Kustomize overlay for deployment", "INFO")
        success = generate_kustomize_overlay(args, template_vars, project_dir)
        if success:
            print_status("Kustomize overlay generation completed successfully!", "SUCCESS")
        else:
            print_status("Kustomize overlay generation failed", "ERROR")
            sys.exit(1)
        return

    except Exception as e:
        print_status(f"Error in manifest generation: {str(e)}", "ERROR")
        import traceback
        print_status(f"Traceback: {traceback.format_exc()}", "ERROR")
        sys.exit(1)


if __name__ == "__main__":
    main()



